<template>
  <div class="error-page">
    <div class="error-container">
      <h1 class="error-title">{{ error.statusCode }}</h1>
      <h2 class="error-message">{{ error.statusMessage }}</h2>
      
      <div class="error-details">
        <h3>Error Details:</h3>
        <p><strong>Status Code:</strong> {{ error.statusCode }}</p>
        <p><strong>Status Message:</strong> {{ error.statusMessage }}</p>
        <p><strong>Fatal:</strong> {{ error.fatal ? 'Yes' : 'No' }}</p>
        <p><strong>Message:</strong> {{ error.message || 'No additional message' }}</p>
        <p><strong>Stack:</strong> {{ error.stack ? 'Available in console' : 'Not available' }}</p>
      </div>
      
      <div class="error-actions">
        <button @click="handleError" class="btn btn-primary">
          Try Again
        </button>
        <NuxtLink to="/" class="btn btn-secondary">
          Go Home
        </NuxtLink>
        <NuxtLink to="/normal" class="btn btn-info">
          Go to Normal Page
        </NuxtLink>
      </div>
      
      <div class="error-info">
        <h3>About This Error:</h3>
        <p>This error was caught by Nuxt's error handling system. You can:</p>
        <ul>
          <li>Click "Try Again" to attempt to clear the error and retry</li>
          <li>Navigate to a different page using the buttons above</li>
          <li>Check the browser console for more detailed error information</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
// Define props for the error
const props = defineProps({
  error: Object
})

// Log the error details
console.log('Error page rendered with error:', props.error)

// Handle error clearing
const handleError = async () => {
  console.log('Attempting to clear error and retry...')
  await clearError({ redirect: '/' })
}

// Log when error page mounts
onMounted(() => {
  console.log('Error page mounted')
  console.log('Error details:', props.error)
})
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 20px;
}

.error-container {
  max-width: 600px;
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-title {
  font-size: 4rem;
  color: #dc3545;
  margin-bottom: 10px;
  font-weight: bold;
}

.error-message {
  font-size: 1.5rem;
  color: #495057;
  margin-bottom: 30px;
}

.error-details {
  text-align: left;
  margin: 30px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.error-details h3 {
  margin-bottom: 15px;
  color: #495057;
}

.error-details p {
  margin: 8px 0;
  font-family: monospace;
  font-size: 0.9rem;
}

.error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin: 30px 0;
}

.btn {
  padding: 12px 20px;
  text-decoration: none;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  border: none;
  cursor: pointer;
  display: inline-block;
}

.btn-primary {
  background-color: #007bff;
}

.btn-secondary {
  background-color: #6c757d;
}

.btn-info {
  background-color: #17a2b8;
}

.btn:hover {
  opacity: 0.8;
}

.error-info {
  text-align: left;
  margin-top: 30px;
  padding: 20px;
  background-color: #e9ecef;
  border-radius: 5px;
}

.error-info h3 {
  margin-bottom: 15px;
  color: #495057;
}

.error-info ul {
  margin-left: 20px;
}

.error-info li {
  margin: 8px 0;
}
</style>
