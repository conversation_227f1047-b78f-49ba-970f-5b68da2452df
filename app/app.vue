<template>
  <div>
    <NuxtRouteAnnouncer />

    <!-- Debug Info -->
    <div style="background: yellow; padding: 10px; margin: 10px;">
      <strong>Debug:</strong> Current route: {{ $route.path }} | Error: {{ error ? 'Yes' : 'No' }}
    </div>

    <!-- Navigation Header -->
    <nav class="navbar">
      <div class="nav-container">
        <h1 class="nav-title">Nuxt Error Testing</h1>
        <div class="nav-links">
          <NuxtLink to="/" class="nav-link">Home</NuxtLink>
          <NuxtLink to="/test" class="nav-link">Test</NuxtLink>
          <NuxtLink to="/normal" class="nav-link">Normal Page</NuxtLink>
          <NuxtLink to="/error-test" class="nav-link">Error Test Page</NuxtLink>
        </div>
      </div>
    </nav>

    <!-- Error Display -->
    <div v-if="error" class="error-banner">
      <div class="error-content">
        <h3>{{ error.statusCode }} - {{ error.statusMessage }}</h3>
        <p><strong>Fatal:</strong> {{ error.fatal ? 'Yes' : 'No' }}</p>
        <p><strong>Message:</strong> {{ error.message || 'No additional message' }}</p>
        <button @click="clearErrorHandler" class="clear-error-btn">Clear Error</button>
      </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
      <div style="border: 2px solid red; padding: 10px; margin: 10px;">
        <strong>Page Content Area:</strong>
        <NuxtPage />
      </div>
    </main>
  </div>
</template>

<script setup>
// Get error state
const error = useError()
const route = useRoute()

console.log('App.vue setup running')
console.log('Initial route:', route.path)
console.log('Initial error:', error.value)

// Method to clear error
const clearErrorHandler = () => {
  console.log('Clearing error from app.vue...')
  clearError()
}

// Watch for route changes and log error state
watch(() => route.path, (newPath, oldPath) => {
  console.log(`Route changed from ${oldPath} to ${newPath}`)
  console.log('Error state after route change:', error.value)
})

// Log error changes
watch(error, (newError, oldError) => {
  console.log('Error state changed in app.vue:', { oldError, newError })
}, { deep: true })

// Log when app mounts
onMounted(() => {
  console.log('App.vue mounted')
  console.log('Route on mount:', route.path)
  console.log('Error on mount:', error.value)
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
}

.navbar {
  background-color: #343a40;
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-title {
  font-size: 1.5rem;
  font-weight: bold;
}

.nav-links {
  display: flex;
  gap: 20px;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.router-link-active {
  background-color: #007bff;
}

.error-banner {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 15px;
  margin: 0;
}

.error-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.error-content h3 {
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.error-content p {
  margin: 5px 0;
}

.clear-error-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.clear-error-btn:hover {
  background-color: #c82333;
}

.main-content {
  min-height: calc(100vh - 80px);
}
</style>
