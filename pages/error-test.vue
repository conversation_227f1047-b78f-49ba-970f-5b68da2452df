<template>
  <div class="container">
    <h1>Error Test Page</h1>
    <p>This page uses middleware that can throw errors based on query parameters.</p>
    
    <div class="controls">
      <h2>Test Error Scenarios:</h2>
      
      <div class="button-group">
        <NuxtLink to="/error-test" class="btn btn-success">
          Load Normal (No Error)
        </NuxtLink>
        
        <NuxtLink to="/error-test?error=true" class="btn btn-warning">
          Trigger Non-Fatal Error
        </NuxtLink>
        
        <NuxtLink to="/error-test?fatal=true" class="btn btn-danger">
          Trigger Fatal Error
        </NuxtLink>
      </div>
      
      <div class="navigation">
        <h3>Navigation Test:</h3>
        <NuxtLink to="/normal" class="btn btn-info">
          Go to Normal Page
        </NuxtLink>
      </div>
    </div>
    
    <div class="info">
      <h3>Current State:</h3>
      <p><strong>Route:</strong> {{ $route.path }}</p>
      <p><strong>Query:</strong> {{ JSON.stringify($route.query) }}</p>
      <p><strong>Error State:</strong> {{ error ? 'Error Present' : 'No Error' }}</p>
    </div>
  </div>
</template>

<script setup>
// This page uses the error-test middleware
definePageMeta({
  middleware: 'error-test'
})

// Get current error state
const error = useError()

// Log when component mounts
onMounted(() => {
  console.log('Error test page mounted')
  console.log('Current error:', error.value)
})
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
}

.button-group {
  display: flex;
  gap: 10px;
  margin: 10px 0;
  flex-wrap: wrap;
}

.navigation {
  margin-top: 30px;
}

.btn {
  padding: 10px 15px;
  text-decoration: none;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  display: inline-block;
}

.btn-success {
  background-color: #28a745;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-danger {
  background-color: #dc3545;
}

.btn-info {
  background-color: #17a2b8;
}

.btn:hover {
  opacity: 0.8;
}

.info {
  margin-top: 30px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.info p {
  margin: 5px 0;
}
</style>
