<template>
  <div class="container">
    <h1>Normal Page</h1>
    <p>This page loads without any errors and doesn't use any middleware that throws errors.</p>
    
    <div class="controls">
      <h2>Navigation Test:</h2>
      
      <div class="button-group">
        <NuxtLink to="/error-test" class="btn btn-primary">
          Go to Error Test Page
        </NuxtLink>
        
        <NuxtLink to="/error-test?error=true" class="btn btn-warning">
          Go to Error Test (Non-Fatal)
        </NuxtLink>
        
        <NuxtLink to="/error-test?fatal=true" class="btn btn-danger">
          Go to Error Test (Fatal)
        </NuxtLink>
      </div>
    </div>
    
    <div class="info">
      <h3>Current State:</h3>
      <p><strong>Route:</strong> {{ $route.path }}</p>
      <p><strong>Query:</strong> {{ JSON.stringify($route.query) }}</p>
      <p><strong>Error State:</strong> {{ error ? 'Error Present' : 'No Error' }}</p>
      <p><strong>Page Load Time:</strong> {{ loadTime }}</p>
    </div>
    
    <div class="error-clearing-test">
      <h3>Error Clearing Test:</h3>
      <p>Use this page to test if errors are properly cleared when navigating away from error pages.</p>
      <button @click="clearError" class="btn btn-secondary">
        Manually Clear Error
      </button>
      <button @click="checkErrorState" class="btn btn-info">
        Check Error State
      </button>
    </div>
  </div>
</template>

<script setup>
// Get current error state
const error = useError()
const loadTime = ref('')

// Methods for testing error clearing
const clearError = () => {
  console.log('Manually clearing error...')
  clearError()
  console.log('Error after manual clear:', error.value)
}

const checkErrorState = () => {
  console.log('Current error state:', error.value)
  alert(`Error state: ${error.value ? 'Error Present' : 'No Error'}`)
}

// Log when component mounts
onMounted(() => {
  loadTime.value = new Date().toLocaleTimeString()
  console.log('Normal page mounted at:', loadTime.value)
  console.log('Current error on normal page:', error.value)
})

// Watch for error changes
watch(error, (newError, oldError) => {
  console.log('Error state changed on normal page:', { oldError, newError })
}, { deep: true })
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin: 20px 0;
}

.button-group {
  display: flex;
  gap: 10px;
  margin: 10px 0;
  flex-wrap: wrap;
}

.error-clearing-test {
  margin-top: 30px;
  padding: 15px;
  background-color: #e9ecef;
  border-radius: 5px;
}

.btn {
  padding: 10px 15px;
  text-decoration: none;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  display: inline-block;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: #007bff;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-danger {
  background-color: #dc3545;
}

.btn-secondary {
  background-color: #6c757d;
}

.btn-info {
  background-color: #17a2b8;
}

.btn:hover {
  opacity: 0.8;
}

.info {
  margin-top: 30px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.info p {
  margin: 5px 0;
}
</style>
