<template>
  <div class="container">
    <h1>Nuxt Error Testing Project</h1>
    <p>This project is designed to test how <PERSON><PERSON><PERSON> handles errors during navigation, specifically:</p>
    
    <div class="features">
      <h2>Testing Features:</h2>
      <ul>
        <li><strong>Middleware Errors:</strong> Test fatal and non-fatal errors thrown from route middleware</li>
        <li><strong>Error Persistence:</strong> See how errors persist or clear during navigation</li>
        <li><strong>Error State Management:</strong> Observe how N<PERSON><PERSON> manages error state across routes</li>
      </ul>
    </div>
    
    <div class="test-scenarios">
      <h2>Test Scenarios:</h2>
      
      <div class="scenario">
        <h3>1. Normal Navigation</h3>
        <p>Navigate between pages without errors to establish baseline behavior.</p>
        <NuxtLink to="/normal" class="btn btn-primary">Go to Normal Page</NuxtLink>
      </div>
      
      <div class="scenario">
        <h3>2. Non-Fatal Error</h3>
        <p>Trigger a non-fatal error in middleware and observe behavior.</p>
        <NuxtLink to="/error-test?error=true" class="btn btn-warning">Trigger Non-Fatal Error</NuxtLink>
      </div>
      
      <div class="scenario">
        <h3>3. Fatal Error</h3>
        <p>Trigger a fatal error in middleware and observe behavior.</p>
        <NuxtLink to="/error-test?fatal=true" class="btn btn-danger">Trigger Fatal Error</NuxtLink>
      </div>
      
      <div class="scenario">
        <h3>4. Error Recovery</h3>
        <p>Test navigation after errors to see how Nuxt clears error state.</p>
        <div class="button-group">
          <NuxtLink to="/error-test" class="btn btn-success">Error Test (Clean)</NuxtLink>
          <NuxtLink to="/normal" class="btn btn-info">Normal Page</NuxtLink>
        </div>
      </div>
    </div>
    
    <div class="instructions">
      <h2>How to Test:</h2>
      <ol>
        <li>Open browser developer tools to see console logs</li>
        <li>Navigate through different scenarios using the buttons above</li>
        <li>Observe the error banner at the top when errors occur</li>
        <li>Watch console logs for error state changes during navigation</li>
        <li>Test manual error clearing using the "Clear Error" button</li>
      </ol>
    </div>
    
    <div class="current-state">
      <h3>Current State:</h3>
      <p><strong>Route:</strong> {{ $route.path }}</p>
      <p><strong>Error State:</strong> {{ error ? 'Error Present' : 'No Error' }}</p>
      <p><strong>Load Time:</strong> {{ loadTime }}</p>
    </div>
  </div>
</template>

<script setup>
// Get current error state
const error = useError()
const loadTime = ref('')

// Log when component mounts
onMounted(() => {
  loadTime.value = new Date().toLocaleTimeString()
  console.log('Index page mounted at:', loadTime.value)
  console.log('Current error on index page:', error.value)
})
</script>

<style scoped>
.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.features {
  margin: 30px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.features ul {
  margin-left: 20px;
}

.features li {
  margin: 10px 0;
}

.test-scenarios {
  margin: 30px 0;
}

.scenario {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background-color: #ffffff;
}

.scenario h3 {
  color: #495057;
  margin-bottom: 10px;
}

.scenario p {
  margin-bottom: 15px;
  color: #6c757d;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 15px;
  text-decoration: none;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  display: inline-block;
  margin: 5px 0;
}

.btn-primary {
  background-color: #007bff;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-danger {
  background-color: #dc3545;
}

.btn-success {
  background-color: #28a745;
}

.btn-info {
  background-color: #17a2b8;
}

.btn:hover {
  opacity: 0.8;
}

.instructions {
  margin: 30px 0;
  padding: 20px;
  background-color: #e9ecef;
  border-radius: 8px;
}

.instructions ol {
  margin-left: 20px;
}

.instructions li {
  margin: 8px 0;
}

.current-state {
  margin-top: 30px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.current-state p {
  margin: 5px 0;
}
</style>
