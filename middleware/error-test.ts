export default defineNuxtRouteMiddleware((to, from) => {
  console.log('Error test middleware running for:', to.path)
  
  // Check if we should throw a fatal error
  if (to.query.fatal === 'true') {
    console.log('Throwing fatal error...')
    throw createError({
      statusCode: 500,
      statusMessage: 'Fatal error from middleware',
      fatal: true
    })
  }
  
  // Check if we should throw a non-fatal error
  if (to.query.error === 'true') {
    console.log('Throwing non-fatal error...')
    throw createError({
      statusCode: 400,
      statusMessage: 'Non-fatal error from middleware',
      fatal: false
    })
  }
  
  console.log('Middleware completed without errors')
})
